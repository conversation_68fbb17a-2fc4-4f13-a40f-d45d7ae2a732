<template>
  <div class="dynamic-field-renderer">
    <!-- 文本类型 -->
    <span v-if="fieldType === 'text'" class="text-gray-900">
      {{ displayValue }}
    </span>

    <!-- 数字类型 -->
    <span v-else-if="fieldType === 'number'" class="text-gray-900 font-mono">
      {{ formatNumber(value) }}
    </span>

    <!-- 货币类型 -->
    <span
      v-else-if="fieldType === 'currency'"
      class="text-green-600 font-semibold"
    >
      {{ formatCurrency(value) }}
    </span>

    <!-- 日期类型 -->
    <span v-else-if="fieldType === 'date'" class="text-blue-600">
      {{ formatDate(value) }}
    </span>

    <!-- 日期时间类型 -->
    <span v-else-if="fieldType === 'datetime'" class="text-blue-600">
      {{ formatDateTime(value) }}
    </span>

    <!-- 布尔类型 -->
    <span v-else-if="fieldType === 'boolean'" class="inline-flex items-center">
      <span
        :class="[
          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800',
        ]"
      >
        <svg
          :class="['w-3 h-3 mr-1', value ? 'text-green-500' : 'text-red-500']"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            v-if="value"
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          />
          <path
            v-else
            fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
        {{ value ? '是' : '否' }}
      </span>
    </span>

    <!-- 枚举类型 -->
    <span v-else-if="fieldType === 'enum'" class="inline-flex items-center">
      <span
        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
      >
        {{ getEnumDisplayValue(value) }}
      </span>
    </span>

    <!-- 状态类型 -->
    <span v-else-if="fieldType === 'status'" class="inline-flex items-center">
      <span
        :class="[
          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
          getStatusClass(value),
        ]"
      >
        <span
          :class="['w-2 h-2 rounded-full mr-1', getStatusDotClass(value)]"
        ></span>
        {{ getStatusDisplayValue(value) }}
      </span>
    </span>

    <!-- 邮箱类型 -->
    <a
      v-else-if="fieldType === 'email'"
      :href="`mailto:${value}`"
      class="text-blue-600 hover:text-blue-800 underline"
    >
      {{ value }}
    </a>

    <!-- 电话类型 -->
    <a
      v-else-if="fieldType === 'phone'"
      :href="`tel:${value}`"
      class="text-green-600 hover:text-green-800 underline"
    >
      {{ value }}
    </a>

    <!-- URL 类型 -->
    <a
      v-else-if="fieldType === 'url'"
      :href="value"
      target="_blank"
      rel="noopener noreferrer"
      class="text-blue-600 hover:text-blue-800 underline"
    >
      {{ value }}
    </a>

    <!-- 用户类型 -->
    <span v-else-if="fieldType === 'user'" class="inline-flex items-center">
      <span v-if="userLoading" class="text-gray-500 text-sm">
        <svg
          class="w-3 h-3 animate-spin mr-1"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          ></path>
        </svg>
        加载中...
      </span>
      <span v-else-if="userError" class="text-red-500 text-sm">
        ID: {{ value }}
      </span>
      <span v-else-if="userName" class="text-blue-600 font-medium">
        {{ userName }}
      </span>
      <span v-else class="text-gray-500"> ID: {{ value }} </span>
    </span>

    <!-- 关系类型 -->
    <span v-else-if="fieldType === 'relation'" class="text-purple-600">
      {{ getRelationDisplayValue(value) }}
    </span>

    <!-- 默认类型 -->
    <span v-else class="text-gray-900">
      {{ displayValue }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'
import userCache from '@/utils/userCache'

interface Props {
  value: any
  fieldInfo: any
  fieldType?: string
}

const props = defineProps<Props>()

// 用户名缓存
const userName = ref<string | null>(null)
const userLoading = ref(false)
const userError = ref(false)

// 计算显示值
const displayValue = computed(() => {
  if (props.value === null || props.value === undefined) {
    return '--'
  }
  return String(props.value)
})

// 推断字段类型
const fieldType = computed(() => {
  if (props.fieldType) return props.fieldType

  const fieldInfo = props.fieldInfo
  if (!fieldInfo) return 'text'

  // 根据字段信息推断类型
  if (fieldInfo.type) {
    switch (fieldInfo.type.toLowerCase()) {
      case 'integer':
      case 'float':
      case 'decimal':
        return 'number'
      case 'boolean':
        return 'boolean'
      case 'date':
        return 'date'
      case 'datetime':
      case 'timestamp':
        return 'datetime'
      case 'enum':
        return 'enum'
      case 'relation':
        return 'relation'
      default:
        break
    }
  }

  // 根据字段名推断类型
  const fieldName = fieldInfo.name?.toLowerCase() || ''

  // 添加调试日志
  console.log('DynamicFieldRenderer 字段类型推断:', {
    fieldName,
    fieldInfo,
    value: props.value,
  })

  // 用户字段检测
  if (
    fieldName.includes('created_by') ||
    fieldName.includes('updated_by') ||
    fieldName.includes('author_id') ||
    fieldName.includes('user_id') ||
    fieldName.includes('creator') ||
    fieldName.includes('modifier') ||
    fieldName.endsWith('_by') ||
    fieldName === 'created_by' ||
    fieldName === 'updated_by' ||
    // 添加更多常见的用户字段模式
    fieldName.startsWith('created_by') ||
    fieldName.startsWith('updated_by') ||
    fieldName.includes('create_by') ||
    fieldName.includes('update_by')
  ) {
    console.log('DynamicFieldRenderer 识别为用户字段:', fieldName)
    return 'user'
  }

  if (fieldName.includes('email')) return 'email'
  if (fieldName.includes('phone') || fieldName.includes('tel')) return 'phone'
  if (fieldName.includes('url') || fieldName.includes('link')) return 'url'
  if (fieldName.includes('status')) return 'status'
  if (
    fieldName.includes('amount') ||
    fieldName.includes('price') ||
    fieldName.includes('cost')
  )
    return 'currency'
  if (fieldName.includes('date') || fieldName.includes('time'))
    return 'datetime'

  const inferredType = 'text'
  console.log('DynamicFieldRenderer 最终推断类型:', {
    fieldName,
    inferredType,
    value: props.value,
  })
  return inferredType
})

// 格式化数字
const formatNumber = (value: any) => {
  if (value === null || value === undefined || value === '') return '--'
  const num = Number(value)
  if (isNaN(num)) return value
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 格式化货币
const formatCurrency = (value: any) => {
  if (value === null || value === undefined || value === '') return '--'
  const num = Number(value)
  if (isNaN(num)) return value
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(num)
}

// 格式化日期
const formatDate = (value: any) => {
  if (!value) return '--'
  try {
    const date = new Date(value)
    if (isNaN(date.getTime())) return value
    return date.toLocaleDateString('zh-CN')
  } catch {
    return value
  }
}

// 格式化日期时间
const formatDateTime = (value: any) => {
  if (!value) return '--'
  try {
    const date = new Date(value)
    if (isNaN(date.getTime())) return value
    return date.toLocaleString('zh-CN')
  } catch {
    return value
  }
}

// 获取枚举显示值
const getEnumDisplayValue = (value: any) => {
  const enumInfo = props.fieldInfo?.enum_info
  if (enumInfo && enumInfo.enum_values && enumInfo.enum_values[value]) {
    return enumInfo.enum_values[value]
  }
  return value || '--'
}

// 获取状态显示值
const getStatusDisplayValue = (value: any) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    pending: '待处理',
    completed: '已完成',
    cancelled: '已取消',
    draft: '草稿',
    published: '已发布',
    archived: '已归档',
  }
  return statusMap[value] || value || '--'
}

// 获取状态样式类
const getStatusClass = (value: any) => {
  const statusClasses: Record<string, string> = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800',
    completed: 'bg-blue-100 text-blue-800',
    cancelled: 'bg-red-100 text-red-800',
    draft: 'bg-gray-100 text-gray-800',
    published: 'bg-green-100 text-green-800',
    archived: 'bg-purple-100 text-purple-800',
  }
  return statusClasses[value] || 'bg-gray-100 text-gray-800'
}

// 获取状态点样式类
const getStatusDotClass = (value: any) => {
  const dotClasses: Record<string, string> = {
    active: 'bg-green-500',
    inactive: 'bg-gray-500',
    pending: 'bg-yellow-500',
    completed: 'bg-blue-500',
    cancelled: 'bg-red-500',
    draft: 'bg-gray-500',
    published: 'bg-green-500',
    archived: 'bg-purple-500',
  }
  return dotClasses[value] || 'bg-gray-500'
}

// 获取关系显示值
const getRelationDisplayValue = (value: any) => {
  if (!value) return '--'
  if (typeof value === 'object') {
    return (
      value.name ||
      value.title ||
      value.nick_name ||
      value.code ||
      value.id ||
      '--'
    )
  }
  return value
}

// 加载用户信息
const loadUserInfo = async (userId: number | string) => {
  if (!userId || userId === null || userId === undefined) {
    userName.value = null
    return
  }

  userLoading.value = true
  userError.value = false
  userName.value = null

  try {
    // 获取用户名
    const username = await userCache.getUserName(userId)

    if (username) {
      userName.value = username
    } else {
      userName.value = null
      userError.value = true
    }
  } catch (error) {
    console.warn('加载用户信息失败:', error)
    userName.value = null
    userError.value = true
  } finally {
    userLoading.value = false
  }
}

// 监听用户字段的值变化
watch(
  () => [props.value, fieldType.value],
  ([newValue, newFieldType]) => {
    if (newFieldType === 'user' && newValue) {
      loadUserInfo(newValue)
    } else {
      userName.value = null
      userLoading.value = false
      userError.value = false
    }
  },
  { immediate: true }
)

// 组件挂载时初始化
onMounted(() => {
  if (fieldType.value === 'user' && props.value) {
    loadUserInfo(props.value)
  }
})
</script>

<style scoped>
.dynamic-field-renderer {
  display: inline-flex;
  align-items: center;
}
</style>
