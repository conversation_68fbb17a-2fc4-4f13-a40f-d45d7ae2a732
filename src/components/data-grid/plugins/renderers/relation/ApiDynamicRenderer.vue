<template>
  <div class="api-dynamic-renderer">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center p-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
      ></div>
      <span class="ml-2 text-gray-600">正在加载数据...</span>
    </div>

    <!-- 错误状态 -->
    <div
      v-else-if="error"
      class="p-4 bg-red-50 border border-red-200 rounded-lg"
    >
      <div class="flex items-center">
        <svg
          class="w-5 h-5 text-red-500 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
        <h3 class="text-red-800 font-medium">数据加载失败</h3>
      </div>
      <p class="text-red-700 mt-1">{{ error }}</p>
      <button
        @click="retryLoad"
        class="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
      >
        重试
      </button>
    </div>

    <!-- 数据展示 -->
    <div v-else-if="detailData" class="api-detail-content">
      <!-- 标题区域 -->
      <!-- <div class="mb-6 pb-4 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900">
          {{ getTitle() }}
        </h2>
        <p class="text-sm text-gray-500 mt-1">
          {{ moduleModel }} - {{ getSubtitle() }}
        </p>
      </div> -->

      <!-- 数组数据展示 -->
      <div v-if="Array.isArray(detailData)" class="space-y-4">
        <h3 class="text-lg font-medium text-gray-800">
          数据列表 ({{ detailData.length }} 项)
        </h3>
        <div class="space-y-3">
          <div
            v-for="(item, index) in detailData"
            :key="index"
            class="bg-gray-50 rounded-lg p-4 border"
          >
            <div class="flex justify-between items-center mb-2">
              <h4 class="font-medium text-gray-700">项目 {{ index + 1 }}</h4>
              <span class="text-sm text-gray-500"
                >ID: {{ item.id || '--' }}</span
              >
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div
                v-for="(value, key) in getDisplayFields(item)"
                :key="key"
                class="flex justify-between items-start py-1"
              >
                <span
                  class="font-medium text-gray-600 text-sm min-w-0 flex-shrink-0 mr-3"
                >
                  {{ getFieldLabel(key) }}:
                </span>
                <div class="text-right min-w-0 flex-1 text-sm">
                  <DynamicFieldRenderer
                    :value="value"
                    :field-info="getFieldInfo(key)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 单个对象数据展示 -->
      <div v-else class="space-y-6">
        <div
          v-for="group in fieldGroups"
          :key="group.name"
          class="bg-gray-50 rounded-lg p-4"
        >
          <h3 class="text-lg font-medium text-gray-800 mb-3">
            {{ group.label }}
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="field in group.fields"
              :key="field.name"
              class="flex justify-between items-start py-2"
            >
              <span
                class="font-medium text-gray-600 min-w-0 flex-shrink-0 mr-4"
              >
                {{ field.comment || field.name }}:
              </span>
              <div class="text-right min-w-0 flex-1">
                <DynamicFieldRenderer
                  :value="detailData[field.name]"
                  :field-info="field"
                  :field-type="getUserFieldType(field.name)"
                />
                <!-- 添加调试信息 -->
                <div
                  v-if="
                    field.name === 'created_by' || field.name === 'updated_by'
                  "
                  class="text-xs text-gray-400 mt-1"
                >
                  调试: {{ field.name }} = {{ detailData[field.name] }} (类型:
                  {{ field.type || '未知' }}) (推断:
                  {{ getUserFieldType(field.name) }})
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 关系数据展示 -->
      <div v-if="relationFields.length > 0" class="mt-6 space-y-4">
        <h3 class="text-lg font-medium text-gray-800">关联信息</h3>
        <div class="grid grid-cols-1 gap-4">
          <div
            v-for="field in relationFields"
            :key="field.name"
            class="bg-blue-50 rounded-lg p-4"
          >
            <h4 class="font-medium text-blue-800 mb-2">
              {{ field.label || field.name }}
            </h4>
            <DynamicFieldRenderer
              :value="detailData[field.name]"
              :field-info="field"
              field-type="relation"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 空数据状态 -->
    <div v-else class="text-center py-8">
      <svg
        class="w-12 h-12 text-gray-400 mx-auto mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        ></path>
      </svg>
      <p class="text-gray-500">暂无数据</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getApi } from '@/api/apiService'
import type { ModelApi } from '@/api/apiService'
import DynamicFieldRenderer from './DynamicFieldRenderer.vue'

interface Props {
  moduleModel: string
  relationData: any
  apiDataKey?: string
  config: any
}

const props = defineProps<Props>()

const loading = ref(false)
const error = ref<string>('')
const detailData = ref<any>(null)
const metadata = ref<any>(null)

// 获取数据 ID
const getDataId = () => {
  const key = props.apiDataKey || 'id'
  return props.relationData?.[key] || props.relationData?.id
}

// 加载数据
const loadData = async () => {
  const dataId = getDataId()

  console.log('ApiDynamicRenderer 开始加载数据:', {
    moduleModel: props.moduleModel,
    relationData: props.relationData,
    apiDataKey: props.apiDataKey,
    extractedDataId: dataId,
    isArray: Array.isArray(props.relationData),
  })

  if (!dataId) {
    error.value = '缺少数据 ID，无法获取详情'
    console.warn('无法获取数据 ID:', {
      relationData: props.relationData,
      apiDataKey: props.apiDataKey,
    })
    return
  }

  loading.value = true
  error.value = ''

  try {
    console.log('正在获取 API 实例:', props.moduleModel)

    // 获取 API 实例
    const api: ModelApi = await getApi(props.moduleModel)

    console.log('API 实例获取成功:', {
      api,
      hasGetMetadata: typeof api.getMetadata === 'function',
      hasGetDetail: typeof api.getDetail === 'function',
    })

    // 对于数组数据（如订单项列表），直接使用关系数据，不调用 getDetail
    if (Array.isArray(props.relationData)) {
      console.log('检测到数组数据，直接使用关系数据')

      const metadataResponse = await api.getMetadata()

      metadata.value = metadataResponse
      detailData.value = props.relationData

      console.log('API 动态渲染数据加载完成 (数组模式):', {
        moduleModel: props.moduleModel,
        metadata: metadataResponse,
        detail: props.relationData,
        itemCount: props.relationData.length,
      })

      return
    }

    // 并行获取元数据和详情数据
    const [metadataResponse, detailResponse] = await Promise.all([
      api.getMetadata(),
      api.getDetail
        ? api.getDetail(dataId)
        : Promise.resolve(props.relationData),
    ])

    metadata.value = metadataResponse
    detailData.value = detailResponse

    console.log('API 动态渲染数据加载完成:', {
      moduleModel: props.moduleModel,
      metadata: metadataResponse,
      detail: detailResponse,
    })
  } catch (err: any) {
    console.error('API 动态渲染数据加载失败:', err)
    console.error('错误详情:', {
      message: err.message,
      stack: err.stack,
      moduleModel: props.moduleModel,
      dataId,
      relationData: props.relationData,
    })
    error.value = err.message || '数据加载失败'
  } finally {
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  loadData()
}

// 获取标题
const getTitle = () => {
  if (!detailData.value) return '详情'

  // 尝试从数据中获取标题
  const titleFields = [
    'name',
    'title',
    'nick_name',
    'customer_name',
    'product_name',
    'order_no',
  ]
  for (const field of titleFields) {
    if (detailData.value[field]) {
      return detailData.value[field]
    }
  }

  return `${props.moduleModel} 详情`
}

// 获取副标题
const getSubtitle = () => {
  if (!detailData.value) return ''

  const id = detailData.value.id
  const code =
    detailData.value.code ||
    detailData.value.employee_no ||
    detailData.value.order_no

  if (code && id) {
    return `${code} (ID: ${id})`
  } else if (code) {
    return code
  } else if (id) {
    return `ID: ${id}`
  }

  return ''
}

// 字段分组
const fieldGroups = computed(() => {
  if (!metadata.value?.fields || !detailData.value) return []

  const groups = [
    { name: 'basic', label: '基本信息', fields: [] as any[] },
    { name: 'contact', label: '联系信息', fields: [] as any[] },
    { name: 'business', label: '业务信息', fields: [] as any[] },
    { name: 'system', label: '系统信息', fields: [] as any[] },
    { name: 'other', label: '其他信息', fields: [] as any[] },
  ]

  // 分类字段
  metadata.value.fields.forEach((field: any) => {
    // 跳过 ID 字段和关系字段
    if (
      field.name === 'id' ||
      field.name.endsWith('_id') ||
      field.type === 'relation'
    ) {
      return
    }

    // 跳过没有数据的字段
    if (!(field.name in detailData.value)) {
      return
    }

    const fieldName = field.name.toLowerCase()

    if (
      fieldName.includes('email') ||
      fieldName.includes('phone') ||
      fieldName.includes('address')
    ) {
      groups[1].fields.push(field) // 联系信息
    } else if (
      fieldName.includes('amount') ||
      fieldName.includes('price') ||
      fieldName.includes('cost') ||
      fieldName.includes('sales') ||
      fieldName.includes('revenue')
    ) {
      groups[2].fields.push(field) // 业务信息
    } else if (
      fieldName.includes('created') ||
      fieldName.includes('updated') ||
      fieldName.includes('modified') ||
      fieldName.includes('status')
    ) {
      groups[3].fields.push(field) // 系统信息
    } else if (
      fieldName.includes('name') ||
      fieldName.includes('title') ||
      fieldName.includes('code') ||
      fieldName.includes('no')
    ) {
      groups[0].fields.push(field) // 基本信息
    } else {
      groups[4].fields.push(field) // 其他信息
    }
  })

  // 过滤掉空的分组
  return groups.filter((group) => group.fields.length > 0)
})

// 关系字段
const relationFields = computed(() => {
  if (!metadata.value?.fields) return []

  return metadata.value.fields.filter(
    (field: any) =>
      field.type === 'relation' && field.name in (detailData.value || {})
  )
})

// 获取要显示的字段
const getDisplayFields = (data: any) => {
  if (!data || typeof data !== 'object') return {}

  // 过滤掉一些不需要显示的字段
  const excludeFields = [
    'created_at',
    'updated_at',
    'deleted_at',
    'password',
    'token',
  ]
  const result: Record<string, any> = {}

  Object.entries(data).forEach(([key, value]) => {
    if (!excludeFields.includes(key) && value !== null && value !== undefined) {
      result[key] = value
      // 添加调试信息，特别关注用户相关字段
      if (
        key.toLowerCase().includes('created') ||
        key.toLowerCase().includes('updated') ||
        key.toLowerCase().includes('by')
      ) {
        console.log('ApiDynamicRenderer 发现用户相关字段:', {
          key,
          value,
          fieldInfo: getFieldInfo(key),
        })
      }
    }
  })

  return result
}

// 获取字段标签
const getFieldLabel = (fieldName: string) => {
  // 尝试从元数据中获取字段标签
  if (metadata.value?.fields) {
    const field = metadata.value.fields.find((f: any) => f.name === fieldName)
    if (field) {
      return field.comment || field.label || field.name
    }
  }

  // 格式化字段名作为标签
  return fieldName.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
}

// 获取字段信息
const getFieldInfo = (fieldName: string) => {
  if (metadata.value?.fields) {
    return (
      metadata.value.fields.find((f: any) => f.name === fieldName) || {
        name: fieldName,
      }
    )
  }
  return { name: fieldName }
}

// 获取用户字段类型
const getUserFieldType = (fieldName: string) => {
  const lowerFieldName = fieldName.toLowerCase()

  // 检查是否为用户字段
  if (
    lowerFieldName === 'created_by' ||
    lowerFieldName === 'updated_by' ||
    lowerFieldName.includes('created_by') ||
    lowerFieldName.includes('updated_by') ||
    lowerFieldName.includes('author_id') ||
    lowerFieldName.includes('user_id') ||
    lowerFieldName.includes('creator') ||
    lowerFieldName.includes('modifier') ||
    lowerFieldName.endsWith('_by')
  ) {
    return 'user'
  }

  return undefined // 让 DynamicFieldRenderer 自己推断
}

onMounted(() => {
  console.log('ApiDynamicRenderer mounted:', {
    moduleModel: props.moduleModel,
    relationData: props.relationData,
    apiDataKey: props.apiDataKey,
    config: props.config,
  })
  loadData()
})
</script>

<style scoped>
.api-dynamic-renderer {
  max-width: 800px;
  margin: 0 auto;
}

.api-detail-content {
  padding: 0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
